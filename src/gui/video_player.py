"""
视频播放器组件
Video Player Component
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QSlider, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QPixmap, QImage
import cv2
import numpy as np
from typing import Optional

from ..utils.logger import Logger
from ..utils.video_utils import (VideoFrameConverter, VideoLoadValidator,
                                PlaybackStateManager, VideoErrorHandler,
                                VideoUIHelper, VideoProcessingHelper)
from ..video.video_loader import VideoLoader


class VideoPlaybackThread(QThread):
    """视频播放线程"""

    frame_ready = pyqtSignal(np.ndarray, int)  # 帧数据和帧号
    position_changed = pyqtSignal(int)  # 播放位置改变
    playback_finished = pyqtSignal()  # 播放完成

    def __init__(self, video_loader: VideoLoader):
        super().__init__()
        self.video_loader = video_loader
        self.current_frame = 0
        self.is_playing = False
        self.should_stop = False
        self.fps = 30.0

    def set_position(self, frame_number: int):
        """设置播放位置"""
        self.current_frame = frame_number

    def set_playing(self, playing: bool):
        """设置播放状态"""
        self.is_playing = playing

    def stop(self):
        """停止播放"""
        self.should_stop = True
        self.is_playing = False

    def run(self):
        """播放线程主循环"""
        if not self.video_loader or not self.video_loader.is_loaded():
            return

        video_info = self.video_loader.get_current_info()
        if not video_info:
            return

        self.fps = video_info.fps
        frame_delay = 1000 / self.fps  # 毫秒

        while not self.should_stop:
            if self.is_playing:
                # 获取当前帧
                frame = self.video_loader.get_frame(self.current_frame)
                if frame is not None:
                    self.frame_ready.emit(frame, self.current_frame)
                    self.position_changed.emit(self.current_frame)

                    # 移动到下一帧
                    self.current_frame += 1

                    # 检查是否播放完成
                    if self.current_frame >= video_info.frame_count:
                        self.playback_finished.emit()
                        self.is_playing = False
                        break

                    # 等待下一帧时间
                    self.msleep(int(frame_delay))
                else:
                    # 无法获取帧，停止播放
                    self.playback_finished.emit()
                    self.is_playing = False
                    break
            else:
                # 暂停状态，短暂等待
                self.msleep(50)


class VideoPlayer(QWidget):
    """视频播放器组件"""

    # 信号定义
    position_changed = pyqtSignal(int)  # 播放位置改变
    play_state_changed = pyqtSignal(bool)  # 播放状态改变

    def __init__(self, title: str = "视频播放器", parent=None):
        super().__init__(parent)

        self.logger = Logger.get_logger(__name__)
        self.title = title
        self.video_loader = VideoLoader()

        # 使用统一的状态管理器和错误处理器
        self.state_manager = PlaybackStateManager(__name__)
        self.error_handler = VideoErrorHandler(__name__)

        # 播放线程
        self.playback_thread = None

        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建组框
        group_box = QGroupBox(self.title)
        group_layout = QVBoxLayout(group_box)

        # 视频显示区域
        self.video_label = QLabel("点击加载视频文件")
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumHeight(200)
        self.video_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        group_layout.addWidget(self.video_label)

        # 进度条
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(100)
        self.progress_slider.setValue(0)
        self.progress_slider.setEnabled(False)
        self.progress_slider.sliderPressed.connect(self.on_slider_pressed)
        self.progress_slider.sliderReleased.connect(self.on_slider_released)
        self.progress_slider.valueChanged.connect(self.on_position_changed)
        group_layout.addWidget(self.progress_slider)

        # 控制按钮
        controls_layout = QHBoxLayout()

        # 播放/暂停按钮
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.toggle_play)
        self.play_btn.setEnabled(False)
        controls_layout.addWidget(self.play_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_video)
        self.stop_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_btn)

        # 加载视频按钮
        self.load_btn = QPushButton("加载视频")
        self.load_btn.clicked.connect(self.load_video_dialog)
        controls_layout.addWidget(self.load_btn)

        controls_layout.addStretch()
        group_layout.addLayout(controls_layout)

        layout.addWidget(group_box)

        # 用于控制进度条拖拽时的播放状态
        self.slider_pressed = False
    
    def load_video_dialog(self):
        """打开文件对话框加载视频"""
        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, f"选择{self.title}文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video(file_path)

    def load_video(self, video_path: str):
        """加载视频"""
        try:
            # 验证视频路径
            if not VideoLoadValidator.validate_video_path(video_path):
                return self.error_handler.handle_video_load_error(video_path,
                    Exception("视频路径无效或文件不存在"))

            # 停止当前播放
            self.stop_video()

            # 加载新视频
            video_info = self.video_loader.load_video(video_path)
            if video_info and video_info.is_valid:
                # 更新状态管理器
                self.state_manager.set_video_info(video_path, video_info.frame_count, video_info.fps)

                # 更新UI
                import os
                filename = os.path.basename(video_path)
                self.video_label.setText(f"已加载: {filename}")

                VideoUIHelper.update_progress_slider(
                    self.progress_slider, 0, video_info.frame_count)
                VideoUIHelper.enable_video_controls(
                    self.play_btn, self.stop_btn, self.progress_slider, True)

                # 显示第一帧
                self.show_frame(0)

                self.logger.info(f"视频播放器加载视频成功: {video_path}")
                return True
            else:
                return self.error_handler.handle_video_load_error(video_path,
                    Exception("视频信息提取失败"))

        except Exception as e:
            return self.error_handler.handle_video_load_error(video_path, e)

    def show_frame(self, frame_number: int):
        """显示指定帧"""
        try:
            frame = self.video_loader.get_frame(frame_number)
            if frame is not None:
                # 使用统一的帧显示方法
                if VideoFrameConverter.display_frame_in_label(frame, self.video_label):
                    self.state_manager.set_position(frame_number)
                else:
                    self.error_handler.handle_frame_error(frame_number,
                        Exception("帧显示失败"))
            else:
                self.error_handler.handle_frame_error(frame_number,
                    Exception("无法获取帧数据"))

        except Exception as e:
            self.error_handler.handle_frame_error(frame_number, e)

    def toggle_play(self):
        """切换播放状态"""
        if not self.state_manager.is_valid():
            return

        if self.state_manager.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """播放视频"""
        if not self.state_manager.is_valid() or self.state_manager.is_playing:
            return

        def _play_operation():
            self.state_manager.set_playing(True)
            VideoUIHelper.update_play_button_text(self.play_btn, True)

            # 创建并启动播放线程
            if self.playback_thread is None or not self.playback_thread.isRunning():
                self.playback_thread = VideoPlaybackThread(self.video_loader)
                self.playback_thread.frame_ready.connect(self.on_frame_ready)
                self.playback_thread.position_changed.connect(self.on_playback_position_changed)
                self.playback_thread.playback_finished.connect(self.on_playback_finished)

            self.playback_thread.set_position(self.state_manager.current_frame)
            self.playback_thread.set_playing(True)

            if not self.playback_thread.isRunning():
                self.playback_thread.start()

            self.play_state_changed.emit(True)
            return True

        result = VideoProcessingHelper.safe_video_operation(
            "播放视频", _play_operation, self.error_handler)

        if not result:
            self.state_manager.set_playing(False)
            VideoUIHelper.update_play_button_text(self.play_btn, False)

    def pause_video(self):
        """暂停视频"""
        if not self.state_manager.is_playing:
            return

        def _pause_operation():
            self.state_manager.set_playing(False)
            VideoUIHelper.update_play_button_text(self.play_btn, False)

            if self.playback_thread:
                self.playback_thread.set_playing(False)

            self.play_state_changed.emit(False)
            return True

        VideoProcessingHelper.safe_video_operation(
            "暂停视频", _pause_operation, self.error_handler)

    def stop_video(self):
        """停止视频"""
        def _stop_operation():
            self.state_manager.set_playing(False)
            VideoUIHelper.update_play_button_text(self.play_btn, False)

            # 停止播放线程
            if self.playback_thread:
                self.playback_thread.stop()
                self.playback_thread.wait(1000)  # 等待最多1秒
                self.playback_thread = None

            # 重置到第一帧
            self.state_manager.set_position(0)
            if self.state_manager.is_valid():
                self.show_frame(0)
                VideoUIHelper.update_progress_slider(
                    self.progress_slider, 0, self.state_manager.total_frames)

            self.play_state_changed.emit(False)
            return True

        VideoProcessingHelper.safe_video_operation(
            "停止视频", _stop_operation, self.error_handler)

    @pyqtSlot(np.ndarray, int)
    def on_frame_ready(self, frame: np.ndarray, frame_number: int):
        """处理新帧"""
        try:
            # 使用统一的帧显示方法
            VideoFrameConverter.display_frame_in_label(frame, self.video_label)
        except Exception as e:
            self.error_handler.handle_frame_error(frame_number, e)

    @pyqtSlot(int)
    def on_playback_position_changed(self, frame_number: int):
        """播放位置改变"""
        if not self.slider_pressed:  # 只有在用户没有拖拽进度条时才更新
            VideoUIHelper.update_progress_slider(
                self.progress_slider, frame_number, self.state_manager.total_frames)

        self.state_manager.set_position(frame_number)
        self.position_changed.emit(frame_number)

    @pyqtSlot()
    def on_playback_finished(self):
        """播放完成"""
        self.state_manager.set_playing(False)
        VideoUIHelper.update_play_button_text(self.play_btn, False)

        self.state_manager.set_position(0)
        VideoUIHelper.update_progress_slider(
            self.progress_slider, 0, self.state_manager.total_frames)
        self.show_frame(0)

        self.play_state_changed.emit(False)
        self.logger.info("视频播放完成")

    def on_slider_pressed(self):
        """进度条按下"""
        self.slider_pressed = True

    def on_slider_released(self):
        """进度条释放"""
        self.slider_pressed = False
        # 跳转到指定位置
        frame_number = self.progress_slider.value()
        self.seek_to_frame(frame_number)

    def on_position_changed(self, position: int):
        """播放位置改变（用户拖拽进度条）"""
        if self.slider_pressed:
            # 实时显示拖拽位置的帧
            self.show_frame(position)

    def seek_to_frame(self, frame_number: int):
        """跳转到指定帧"""
        if not self.state_manager.is_valid():
            return

        try:
            # 验证并修正帧号
            valid_frame = VideoProcessingHelper.validate_frame_number(
                frame_number, self.state_manager.total_frames)

            self.state_manager.set_position(valid_frame)

            # 如果正在播放，更新播放线程位置
            if self.state_manager.is_playing and self.playback_thread:
                self.playback_thread.set_position(valid_frame)
            else:
                # 如果暂停，直接显示该帧
                self.show_frame(valid_frame)

            VideoUIHelper.update_progress_slider(
                self.progress_slider, valid_frame, self.state_manager.total_frames)
            self.position_changed.emit(valid_frame)

        except Exception as e:
            self.error_handler.handle_playback_error("跳转帧", e)

    def get_current_frame(self) -> int:
        """获取当前帧号"""
        return self.state_manager.current_frame

    def get_total_frames(self) -> int:
        """获取总帧数"""
        return self.state_manager.total_frames

    def is_video_loaded(self) -> bool:
        """检查是否已加载视频"""
        return self.state_manager.is_valid()

    def get_video_path(self) -> Optional[str]:
        """获取视频路径"""
        return self.state_manager.video_path

    def clear_video(self):
        """清除当前视频"""
        try:
            # 停止播放
            self.stop_video()

            # 清除视频加载器
            self.video_loader.clear()

            # 重置状态管理器
            self.state_manager.reset()

            # 重置UI
            self.video_label.setText("点击加载视频文件")
            self.video_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")

            # 重置进度条
            self.progress_slider.setValue(0)
            self.progress_slider.setMaximum(100)
            self.progress_slider.setEnabled(False)

            # 重置按钮状态
            self.play_btn.setText("播放")
            self.play_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

            self.logger.info(f"{self.title} 视频已清除")

        except Exception as e:
            self.error_handler.handle_video_operation_error("清除视频", e)

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.stop_video()
        super().closeEvent(event)
