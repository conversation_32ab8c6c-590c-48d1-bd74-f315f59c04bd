"""
控制面板组件
Control Panel Component
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QLabel, QSlider, QSpinBox, QComboBox, QPushButton,
                             QCheckBox, QDoubleSpinBox, QLineEdit, QTextEdit,
                             QTabWidget, QScrollArea, QFrame, QColorDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPalette

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class ControlPanel(QWidget):
    """控制面板组件"""
    
    # 信号定义
    fusion_params_changed = pyqtSignal(dict)  # 融合参数改变
    parameters_changed = pyqtSignal(dict)     # 参数改变（兼容性）
    preview_requested = pyqtSignal()          # 预览请求（兼容性）
    fusion_requested = pyqtSignal()           # 融合请求（兼容性）
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面 - 合并参数控制界面"""
        layout = QVBoxLayout(self)

        # 创建滚动区域以容纳所有参数
        from PyQt5.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # 合并所有控制参数到一个界面
        self.create_unified_control_panel(content_layout)

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def create_unified_control_panel(self, layout):
        """创建统一的控制面板 - 合并所有参数"""

        # 1. 基础融合设置
        self.create_basic_fusion_group(layout)

        # 2. 时间维度控制
        self.create_time_dimension_group(layout)

        # 3. 空间控制（尺寸和位置合并）
        self.create_spatial_control_group(layout)

        # 4. 图像处理控制
        self.create_image_processing_group(layout)

        # 5. 文字内容控制
        self.create_text_content_group(layout)

        # 6. 输出设置
        self.create_output_settings_group(layout)

        # 添加弹性空间
        layout.addStretch()

    def create_basic_fusion_group(self, parent_layout):
        """创建基础融合设置组"""
        group = QGroupBox("基础融合设置")
        layout = QVBoxLayout(group)

        # 融合类型选择
        fusion_type_layout = QHBoxLayout()
        fusion_type_layout.addWidget(QLabel("融合类型:"))
        self.fusion_type_combo = QComboBox()
        self.fusion_type_combo.addItems([
            "插入融合 (Insertion)",
            "叠加融合 (Overlay)",
            "混合融合 (Blend)"
        ])
        self.fusion_type_combo.currentTextChanged.connect(self.on_fusion_type_changed)
        self.fusion_type_combo.currentTextChanged.connect(self.on_params_changed)
        fusion_type_layout.addWidget(self.fusion_type_combo)
        layout.addLayout(fusion_type_layout)

        # 透明度控制
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setMinimum(0)
        self.alpha_slider.setMaximum(100)
        self.alpha_slider.setValue(50)
        self.alpha_slider.valueChanged.connect(self.on_params_changed)
        alpha_layout.addWidget(self.alpha_slider)
        self.alpha_label = QLabel("50%")
        self.alpha_slider.valueChanged.connect(lambda v: self.alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.alpha_label)
        layout.addLayout(alpha_layout)

        parent_layout.addWidget(group)

    def create_time_dimension_group(self, parent_layout):
        """创建时间维度控制组"""
        group = QGroupBox("时间维度控制")
        layout = QVBoxLayout(group)

        # 插入模式
        insertion_mode_layout = QHBoxLayout()
        insertion_mode_layout.addWidget(QLabel("插入模式:"))
        self.insertion_mode_combo = QComboBox()
        self.insertion_mode_combo.addItems([
            "direct 直接插入",
            "replace 替换插入",
            "segment 分段插入"
        ])
        self.insertion_mode_combo.currentTextChanged.connect(self.on_params_changed)
        insertion_mode_layout.addWidget(self.insertion_mode_combo)
        layout.addLayout(insertion_mode_layout)

        # 插入次数
        insertion_count_layout = QHBoxLayout()
        insertion_count_layout.addWidget(QLabel("插入次数:"))
        self.insertion_count_spinbox = QSpinBox()
        self.insertion_count_spinbox.setMinimum(1)
        self.insertion_count_spinbox.setMaximum(50)
        self.insertion_count_spinbox.setValue(5)
        self.insertion_count_spinbox.valueChanged.connect(self.on_params_changed)
        insertion_count_layout.addWidget(self.insertion_count_spinbox)
        layout.addLayout(insertion_count_layout)

        # 时间分布
        time_distribution_layout = QHBoxLayout()
        time_distribution_layout.addWidget(QLabel("时间分布:"))
        self.time_distribution_combo = QComboBox()
        self.time_distribution_combo.addItems([
            "random 随机分布",
            "uniform 均匀分布",
            "front_heavy 前段偏重",
            "middle_heavy 中段偏重",
            "back_heavy 后段偏重"
        ])
        self.time_distribution_combo.currentTextChanged.connect(self.on_params_changed)
        time_distribution_layout.addWidget(self.time_distribution_combo)
        layout.addLayout(time_distribution_layout)

        # 帧范围
        frame_range_layout = QHBoxLayout()
        frame_range_layout.addWidget(QLabel("帧范围:"))
        self.start_frame_spinbox = QSpinBox()
        self.start_frame_spinbox.setMinimum(0)
        self.start_frame_spinbox.setMaximum(10000)
        self.start_frame_spinbox.setValue(0)
        self.start_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.start_frame_spinbox)
        frame_range_layout.addWidget(QLabel("到"))
        self.end_frame_spinbox = QSpinBox()
        self.end_frame_spinbox.setMinimum(1)
        self.end_frame_spinbox.setMaximum(10000)
        self.end_frame_spinbox.setValue(100)
        self.end_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.end_frame_spinbox)
        layout.addLayout(frame_range_layout)

        parent_layout.addWidget(group)

    def create_spatial_control_group(self, parent_layout):
        """创建空间控制组（合并尺寸和位置控制）"""
        group = QGroupBox("空间控制")
        layout = QVBoxLayout(group)

        # 尺寸控制子组
        size_subgroup = QGroupBox("尺寸控制")
        size_layout = QVBoxLayout(size_subgroup)

        # 尺寸对齐
        size_align_layout = QHBoxLayout()
        self.size_align_cb = QCheckBox("对齐A视频尺寸")
        self.size_align_cb.setChecked(True)
        self.size_align_cb.stateChanged.connect(self.on_params_changed)
        size_align_layout.addWidget(self.size_align_cb)
        size_layout.addLayout(size_align_layout)

        # 缩放比例
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放比例:"))
        self.scale_slider = QSlider(Qt.Horizontal)
        self.scale_slider.setMinimum(10)
        self.scale_slider.setMaximum(200)
        self.scale_slider.setValue(100)
        self.scale_slider.valueChanged.connect(self.on_params_changed)
        scale_layout.addWidget(self.scale_slider)
        self.scale_label = QLabel("100%")
        self.scale_slider.valueChanged.connect(lambda v: self.scale_label.setText(f"{v}%"))
        scale_layout.addWidget(self.scale_label)
        size_layout.addLayout(scale_layout)

        # 缩放模式
        scale_mode_layout = QHBoxLayout()
        scale_mode_layout.addWidget(QLabel("缩放模式:"))
        self.scale_mode_combo = QComboBox()
        self.scale_mode_combo.addItems([
            "fit 等比例缩放",
            "stretch 拉伸缩放",
            "crop 裁剪缩放",
            "pad 填充缩放"
        ])
        self.scale_mode_combo.currentTextChanged.connect(self.on_params_changed)
        scale_mode_layout.addWidget(self.scale_mode_combo)
        size_layout.addLayout(scale_mode_layout)

        layout.addWidget(size_subgroup)

        # 位置控制子组
        position_subgroup = QGroupBox("位置控制")
        position_layout = QVBoxLayout(position_subgroup)

        # 位置模式
        position_mode_layout = QHBoxLayout()
        position_mode_layout.addWidget(QLabel("位置模式:"))
        self.position_mode_combo = QComboBox()
        self.position_mode_combo.addItems([
            "static 静态位置",
            "dynamic 动态位置"
        ])
        self.position_mode_combo.currentTextChanged.connect(self.on_position_mode_changed)
        self.position_mode_combo.currentTextChanged.connect(self.on_params_changed)
        position_mode_layout.addWidget(self.position_mode_combo)
        position_layout.addLayout(position_mode_layout)

        # 静态位置设置
        self.static_position_widget = QWidget()
        static_layout = QVBoxLayout(self.static_position_widget)

        # X位置
        x_pos_layout = QHBoxLayout()
        x_pos_layout.addWidget(QLabel("X位置:"))
        self.x_position_slider = QSlider(Qt.Horizontal)
        self.x_position_slider.setMinimum(0)
        self.x_position_slider.setMaximum(100)
        self.x_position_slider.setValue(50)
        self.x_position_slider.valueChanged.connect(self.on_params_changed)
        x_pos_layout.addWidget(self.x_position_slider)
        self.x_position_label = QLabel("50%")
        self.x_position_slider.valueChanged.connect(lambda v: self.x_position_label.setText(f"{v}%"))
        x_pos_layout.addWidget(self.x_position_label)
        static_layout.addLayout(x_pos_layout)

        # Y位置
        y_pos_layout = QHBoxLayout()
        y_pos_layout.addWidget(QLabel("Y位置:"))
        self.y_position_slider = QSlider(Qt.Horizontal)
        self.y_position_slider.setMinimum(0)
        self.y_position_slider.setMaximum(100)
        self.y_position_slider.setValue(50)
        self.y_position_slider.valueChanged.connect(self.on_params_changed)
        y_pos_layout.addWidget(self.y_position_slider)
        self.y_position_label = QLabel("50%")
        self.y_position_slider.valueChanged.connect(lambda v: self.y_position_label.setText(f"{v}%"))
        y_pos_layout.addWidget(self.y_position_label)
        static_layout.addLayout(y_pos_layout)

        position_layout.addWidget(self.static_position_widget)

        # 动态位置设置
        self.dynamic_position_widget = QWidget()
        dynamic_layout = QVBoxLayout(self.dynamic_position_widget)

        # 运动轨迹
        trajectory_layout = QHBoxLayout()
        trajectory_layout.addWidget(QLabel("运动轨迹:"))
        self.trajectory_combo = QComboBox()
        self.trajectory_combo.addItems([
            "horizontal 水平滚动",
            "vertical 垂直滚动",
            "diagonal 对角线运动",
            "circular 圆形轨迹",
            "random 随机运动"
        ])
        self.trajectory_combo.currentTextChanged.connect(self.on_params_changed)
        trajectory_layout.addWidget(self.trajectory_combo)
        dynamic_layout.addLayout(trajectory_layout)

        # 运动速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("运动速度:"))
        self.movement_speed_slider = QSlider(Qt.Horizontal)
        self.movement_speed_slider.setMinimum(1)
        self.movement_speed_slider.setMaximum(20)
        self.movement_speed_slider.setValue(5)
        self.movement_speed_slider.valueChanged.connect(self.on_params_changed)
        speed_layout.addWidget(self.movement_speed_slider)
        self.speed_label = QLabel("5")
        self.movement_speed_slider.valueChanged.connect(lambda v: self.speed_label.setText(str(v)))
        speed_layout.addWidget(self.speed_label)
        dynamic_layout.addLayout(speed_layout)

        position_layout.addWidget(self.dynamic_position_widget)

        # 初始状态：显示静态位置，隐藏动态位置
        self.dynamic_position_widget.setVisible(False)

        layout.addWidget(position_subgroup)
        parent_layout.addWidget(group)

    def on_position_mode_changed(self, mode):
        """位置模式改变处理"""
        is_dynamic = "dynamic" in mode
        self.static_position_widget.setVisible(not is_dynamic)
        self.dynamic_position_widget.setVisible(is_dynamic)

    def create_image_processing_group(self, parent_layout):
        """创建图像处理控制组"""
        group = QGroupBox("图像处理控制")
        layout = QVBoxLayout(group)

        # 启用预处理
        self.enable_preprocessing_cb = QCheckBox("启用图像预处理")
        self.enable_preprocessing_cb.stateChanged.connect(self.on_preprocessing_enabled_changed)
        self.enable_preprocessing_cb.stateChanged.connect(self.on_params_changed)
        layout.addWidget(self.enable_preprocessing_cb)

        # 预处理选项容器
        self.preprocessing_options_widget = QWidget()
        preprocessing_layout = QVBoxLayout(self.preprocessing_options_widget)

        # 边缘检测
        self.edge_detection_cb = QCheckBox("边缘检测")
        self.edge_detection_cb.stateChanged.connect(self.on_params_changed)
        preprocessing_layout.addWidget(self.edge_detection_cb)

        # 边缘检测方法
        edge_method_layout = QHBoxLayout()
        edge_method_layout.addWidget(QLabel("检测方法:"))
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(["Canny", "Sobel", "Laplacian", "Scharr"])
        self.edge_method_combo.currentTextChanged.connect(self.on_params_changed)
        edge_method_layout.addWidget(self.edge_method_combo)
        preprocessing_layout.addLayout(edge_method_layout)

        # 直方图处理
        self.histogram_cb = QCheckBox("直方图处理")
        self.histogram_cb.stateChanged.connect(self.on_params_changed)
        preprocessing_layout.addWidget(self.histogram_cb)

        # 直方图方法
        hist_method_layout = QHBoxLayout()
        hist_method_layout.addWidget(QLabel("处理方法:"))
        self.histogram_method_combo = QComboBox()
        self.histogram_method_combo.addItems(["equalization", "matching", "gamma_correction"])
        self.histogram_method_combo.currentTextChanged.connect(self.on_params_changed)
        hist_method_layout.addWidget(self.histogram_method_combo)
        preprocessing_layout.addLayout(hist_method_layout)

        # 图像滤镜
        self.filter_cb = QCheckBox("图像滤镜")
        self.filter_cb.stateChanged.connect(self.on_params_changed)
        preprocessing_layout.addWidget(self.filter_cb)

        # 滤镜类型
        filter_type_layout = QHBoxLayout()
        filter_type_layout.addWidget(QLabel("滤镜类型:"))
        self.filter_type_combo = QComboBox()
        self.filter_type_combo.addItems(["gaussian_blur", "sharpen", "denoise"])
        self.filter_type_combo.currentTextChanged.connect(self.on_params_changed)
        filter_type_layout.addWidget(self.filter_type_combo)
        preprocessing_layout.addLayout(filter_type_layout)

        # 混合模式
        blend_mode_layout = QHBoxLayout()
        blend_mode_layout.addWidget(QLabel("混合模式:"))
        self.blend_mode_combo = QComboBox()
        self.blend_mode_combo.addItems([
            "alpha 透明混合",
            "multiply 正片叠底",
            "screen 滤色",
            "overlay 叠加",
            "soft_light 柔光",
            "hard_light 强光"
        ])
        self.blend_mode_combo.currentTextChanged.connect(self.on_params_changed)
        blend_mode_layout.addWidget(self.blend_mode_combo)
        preprocessing_layout.addLayout(blend_mode_layout)

        layout.addWidget(self.preprocessing_options_widget)

        # 初始状态：隐藏预处理选项
        self.preprocessing_options_widget.setVisible(False)

        parent_layout.addWidget(group)

    def on_preprocessing_enabled_changed(self, state):
        """预处理启用状态改变"""
        enabled = state == 2  # Qt.Checked
        self.preprocessing_options_widget.setVisible(enabled)

    def create_text_content_group(self, parent_layout):
        """创建文字内容控制组"""
        group = QGroupBox("文字内容控制")
        layout = QVBoxLayout(group)

        # 启用文字叠加
        self.enable_text_cb = QCheckBox("启用文字叠加")
        self.enable_text_cb.stateChanged.connect(self.on_text_enabled_changed)
        self.enable_text_cb.stateChanged.connect(self.on_params_changed)
        layout.addWidget(self.enable_text_cb)

        # 文字选项容器
        self.text_options_widget = QWidget()
        text_layout = QVBoxLayout(self.text_options_widget)

        # 文字内容
        text_content_layout = QHBoxLayout()
        text_content_layout.addWidget(QLabel("文字内容:"))
        self.text_content_edit = QLineEdit()
        self.text_content_edit.setPlaceholderText("输入要叠加的文字")
        self.text_content_edit.textChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_content_edit)
        text_layout.addLayout(text_content_layout)

        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setMinimum(12)
        self.font_size_slider.setMaximum(72)
        self.font_size_slider.setValue(24)
        self.font_size_slider.valueChanged.connect(self.on_params_changed)
        font_size_layout.addWidget(self.font_size_slider)
        self.font_size_label = QLabel("24")
        self.font_size_slider.valueChanged.connect(lambda v: self.font_size_label.setText(str(v)))
        font_size_layout.addWidget(self.font_size_label)
        text_layout.addLayout(font_size_layout)

        # 文字颜色
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("文字颜色:"))
        self.text_color_combo = QComboBox()
        self.text_color_combo.addItems(["white", "black", "red", "green", "blue", "yellow"])
        self.text_color_combo.currentTextChanged.connect(self.on_params_changed)
        color_layout.addWidget(self.text_color_combo)
        text_layout.addLayout(color_layout)

        # 文字位置
        text_pos_layout = QHBoxLayout()
        text_pos_layout.addWidget(QLabel("文字位置:"))
        self.text_position_combo = QComboBox()
        self.text_position_combo.addItems([
            "center 居中",
            "top_left 左上",
            "top_right 右上",
            "bottom_left 左下",
            "bottom_right 右下"
        ])
        self.text_position_combo.currentTextChanged.connect(self.on_params_changed)
        text_pos_layout.addWidget(self.text_position_combo)
        text_layout.addLayout(text_pos_layout)

        # 文字动画
        text_animation_layout = QHBoxLayout()
        text_animation_layout.addWidget(QLabel("文字动画:"))
        self.text_animation_combo = QComboBox()
        self.text_animation_combo.addItems([
            "none 无动画",
            "horizontal_scroll 水平滚动",
            "vertical_scroll 垂直滚动",
            "fade_in_out 淡入淡出"
        ])
        self.text_animation_combo.currentTextChanged.connect(self.on_params_changed)
        text_animation_layout.addWidget(self.text_animation_combo)
        text_layout.addLayout(text_animation_layout)

        layout.addWidget(self.text_options_widget)

        # 初始状态：隐藏文字选项
        self.text_options_widget.setVisible(False)

        parent_layout.addWidget(group)

    def on_text_enabled_changed(self, state):
        """文字启用状态改变"""
        enabled = state == 2  # Qt.Checked
        self.text_options_widget.setVisible(enabled)

    def create_output_settings_group(self, parent_layout):
        """创建输出设置组"""
        group = QGroupBox("输出设置")
        layout = QVBoxLayout(group)

        # 输出格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("输出格式:"))
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["mp4", "avi", "mov", "mkv"])
        self.output_format_combo.currentTextChanged.connect(self.on_params_changed)
        format_layout.addWidget(self.output_format_combo)
        layout.addLayout(format_layout)

        # 视频质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("视频质量:"))
        self.quality_slider = QSlider(Qt.Horizontal)
        self.quality_slider.setMinimum(1)
        self.quality_slider.setMaximum(10)
        self.quality_slider.setValue(8)
        self.quality_slider.valueChanged.connect(self.on_params_changed)
        quality_layout.addWidget(self.quality_slider)
        self.quality_label = QLabel("8")
        self.quality_slider.valueChanged.connect(lambda v: self.quality_label.setText(str(v)))
        quality_layout.addWidget(self.quality_label)
        layout.addLayout(quality_layout)

        # 帧率
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("输出帧率:"))
        self.output_fps_spinbox = QSpinBox()
        self.output_fps_spinbox.setMinimum(15)
        self.output_fps_spinbox.setMaximum(60)
        self.output_fps_spinbox.setValue(30)
        self.output_fps_spinbox.valueChanged.connect(self.on_params_changed)
        fps_layout.addWidget(self.output_fps_spinbox)
        fps_layout.addWidget(QLabel("fps"))
        layout.addLayout(fps_layout)

        parent_layout.addWidget(group)

    def create_time_dimension_tab(self):
        """创建时间维度控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 融合类型选择
        fusion_type_group = QGroupBox("融合类型")
        fusion_type_layout = QVBoxLayout(fusion_type_group)

        self.fusion_type_combo = QComboBox()
        self.fusion_type_combo.addItems([
            "插入融合 (Insertion)",
            "叠加融合 (Overlay)",
            "混合融合 (Blend)"
        ])
        self.fusion_type_combo.currentTextChanged.connect(self.on_fusion_type_changed)
        fusion_type_layout.addWidget(self.fusion_type_combo)
        layout.addWidget(fusion_type_group)

        # 插入时间控制（仅插入融合时显示）
        self.insertion_time_group = QGroupBox("插入时间控制")
        insertion_time_layout = QVBoxLayout(self.insertion_time_group)

        # 插入模式
        insertion_mode_layout = QHBoxLayout()
        insertion_mode_layout.addWidget(QLabel("插入模式:"))
        self.insertion_mode_combo = QComboBox()
        self.insertion_mode_combo.addItems([
            "直接插入 (Direct)",
            "替换插入 (Replace)",
            "分段插入 (Segment)"
        ])
        self.insertion_mode_combo.currentTextChanged.connect(self.on_params_changed)
        insertion_mode_layout.addWidget(self.insertion_mode_combo)
        insertion_time_layout.addLayout(insertion_mode_layout)

        # 插入频次控制
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(QLabel("插入次数:"))
        self.insertion_count_spinbox = QSpinBox()
        self.insertion_count_spinbox.setMinimum(1)
        self.insertion_count_spinbox.setMaximum(100)
        self.insertion_count_spinbox.setValue(5)
        self.insertion_count_spinbox.valueChanged.connect(self.on_params_changed)
        frequency_layout.addWidget(self.insertion_count_spinbox)
        insertion_time_layout.addLayout(frequency_layout)

        # 时间分布模式
        distribution_layout = QHBoxLayout()
        distribution_layout.addWidget(QLabel("时间分布:"))
        self.time_distribution_combo = QComboBox()
        self.time_distribution_combo.addItems([
            "随机分布 (Random)",
            "均匀分布 (Uniform)",
            "前段偏向 (Early)",
            "中段偏向 (Middle)",
            "后段偏向 (Late)",
            "自定义位置 (Custom)"
        ])
        self.time_distribution_combo.currentTextChanged.connect(self.on_params_changed)
        distribution_layout.addWidget(self.time_distribution_combo)
        insertion_time_layout.addLayout(distribution_layout)

        layout.addWidget(self.insertion_time_group)

        # 帧范围控制
        frame_range_group = QGroupBox("帧范围控制")
        frame_range_layout = QVBoxLayout(frame_range_group)

        # 起始帧
        start_frame_layout = QHBoxLayout()
        start_frame_layout.addWidget(QLabel("起始帧:"))
        self.start_frame_spinbox = QSpinBox()
        self.start_frame_spinbox.setMinimum(0)
        self.start_frame_spinbox.setMaximum(10000)
        self.start_frame_spinbox.setValue(0)
        self.start_frame_spinbox.valueChanged.connect(self.on_params_changed)
        start_frame_layout.addWidget(self.start_frame_spinbox)
        frame_range_layout.addLayout(start_frame_layout)

        # 结束帧
        end_frame_layout = QHBoxLayout()
        end_frame_layout.addWidget(QLabel("结束帧:"))
        self.end_frame_spinbox = QSpinBox()
        self.end_frame_spinbox.setMinimum(1)
        self.end_frame_spinbox.setMaximum(10000)
        self.end_frame_spinbox.setValue(100)
        self.end_frame_spinbox.valueChanged.connect(self.on_params_changed)
        end_frame_layout.addWidget(self.end_frame_spinbox)
        frame_range_layout.addLayout(end_frame_layout)

        layout.addWidget(frame_range_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "1. 时间维度")

    def create_spatial_size_tab(self):
        """创建空间尺寸控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 尺寸调整模式
        size_mode_group = QGroupBox("尺寸调整模式")
        size_mode_layout = QVBoxLayout(size_mode_group)

        self.resize_mode_combo = QComboBox()
        self.resize_mode_combo.addItems([
            "等比例缩放 (Fit)",
            "拉伸适配 (Stretch)",
            "裁剪适配 (Crop)",
            "填充适配 (Fill)",
            "保持原尺寸 (Original)"
        ])
        self.resize_mode_combo.currentTextChanged.connect(self.on_params_changed)
        size_mode_layout.addWidget(self.resize_mode_combo)
        layout.addWidget(size_mode_group)

        # 缩放参数
        scale_group = QGroupBox("缩放参数")
        scale_layout = QVBoxLayout(scale_group)

        # 缩放比例
        scale_ratio_layout = QHBoxLayout()
        scale_ratio_layout.addWidget(QLabel("缩放比例:"))
        self.scale_ratio_spinbox = QDoubleSpinBox()
        self.scale_ratio_spinbox.setMinimum(0.1)
        self.scale_ratio_spinbox.setMaximum(5.0)
        self.scale_ratio_spinbox.setValue(1.0)
        self.scale_ratio_spinbox.setSingleStep(0.1)
        self.scale_ratio_spinbox.valueChanged.connect(self.on_params_changed)
        scale_ratio_layout.addWidget(self.scale_ratio_spinbox)
        scale_layout.addLayout(scale_ratio_layout)

        # 保持长宽比
        self.keep_aspect_ratio_cb = QCheckBox("保持长宽比")
        self.keep_aspect_ratio_cb.setChecked(True)
        self.keep_aspect_ratio_cb.stateChanged.connect(self.on_params_changed)
        scale_layout.addWidget(self.keep_aspect_ratio_cb)

        layout.addWidget(scale_group)

        # 对齐方式
        alignment_group = QGroupBox("对齐方式")
        alignment_layout = QVBoxLayout(alignment_group)

        self.alignment_combo = QComboBox()
        self.alignment_combo.addItems([
            "居中对齐 (Center)",
            "左上对齐 (Top-Left)",
            "右上对齐 (Top-Right)",
            "左下对齐 (Bottom-Left)",
            "右下对齐 (Bottom-Right)"
        ])
        self.alignment_combo.currentTextChanged.connect(self.on_params_changed)
        alignment_layout.addWidget(self.alignment_combo)
        layout.addWidget(alignment_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "2. 空间尺寸")

    def create_spatial_position_tab(self):
        """创建空间位置控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 位置模式选择
        position_mode_group = QGroupBox("位置模式")
        position_mode_layout = QVBoxLayout(position_mode_group)

        self.position_mode_combo = QComboBox()
        self.position_mode_combo.addItems([
            "静态位置 (Static)",
            "动态运动 (Dynamic)"
        ])
        self.position_mode_combo.currentTextChanged.connect(self.on_position_mode_changed)
        position_mode_layout.addWidget(self.position_mode_combo)
        layout.addWidget(position_mode_group)

        # 静态位置控制
        self.static_position_group = QGroupBox("静态位置设置")
        static_position_layout = QVBoxLayout(self.static_position_group)

        # X位置
        x_position_layout = QHBoxLayout()
        x_position_layout.addWidget(QLabel("X位置 (%):"))
        self.static_x_spinbox = QDoubleSpinBox()
        self.static_x_spinbox.setMinimum(0.0)
        self.static_x_spinbox.setMaximum(100.0)
        self.static_x_spinbox.setValue(50.0)
        self.static_x_spinbox.setSingleStep(1.0)
        self.static_x_spinbox.valueChanged.connect(self.on_params_changed)
        x_position_layout.addWidget(self.static_x_spinbox)
        static_position_layout.addLayout(x_position_layout)

        # Y位置
        y_position_layout = QHBoxLayout()
        y_position_layout.addWidget(QLabel("Y位置 (%):"))
        self.static_y_spinbox = QDoubleSpinBox()
        self.static_y_spinbox.setMinimum(0.0)
        self.static_y_spinbox.setMaximum(100.0)
        self.static_y_spinbox.setValue(50.0)
        self.static_y_spinbox.setSingleStep(1.0)
        self.static_y_spinbox.valueChanged.connect(self.on_params_changed)
        y_position_layout.addWidget(self.static_y_spinbox)
        static_position_layout.addLayout(y_position_layout)

        layout.addWidget(self.static_position_group)

        # 动态位置控制
        self.dynamic_position_group = QGroupBox("动态运动设置")
        dynamic_position_layout = QVBoxLayout(self.dynamic_position_group)

        # 运动轨迹
        trajectory_layout = QHBoxLayout()
        trajectory_layout.addWidget(QLabel("运动轨迹:"))
        self.motion_trajectory_combo = QComboBox()
        self.motion_trajectory_combo.addItems([
            "水平滚动 (Horizontal)",
            "垂直滚动 (Vertical)",
            "圆形轨迹 (Circular)",
            "对角线 (Diagonal)",
            "自定义路径 (Custom)"
        ])
        self.motion_trajectory_combo.currentTextChanged.connect(self.on_params_changed)
        trajectory_layout.addWidget(self.motion_trajectory_combo)
        dynamic_position_layout.addLayout(trajectory_layout)

        # 运动速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("运动速度:"))
        self.motion_speed_spinbox = QDoubleSpinBox()
        self.motion_speed_spinbox.setMinimum(0.1)
        self.motion_speed_spinbox.setMaximum(10.0)
        self.motion_speed_spinbox.setValue(1.0)
        self.motion_speed_spinbox.setSingleStep(0.1)
        self.motion_speed_spinbox.valueChanged.connect(self.on_params_changed)
        speed_layout.addWidget(self.motion_speed_spinbox)
        dynamic_position_layout.addLayout(speed_layout)

        layout.addWidget(self.dynamic_position_group)

        # 初始状态设置
        self.dynamic_position_group.setVisible(False)

        layout.addStretch()
        self.tab_widget.addTab(tab, "3. 空间位置")

    def create_image_processing_tab(self):
        """创建图像内容处理控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 预处理控制
        preprocessing_group = QGroupBox("图像预处理")
        preprocessing_layout = QVBoxLayout(preprocessing_group)

        self.enable_preprocessing_cb = QCheckBox("启用图像预处理")
        self.enable_preprocessing_cb.stateChanged.connect(self.on_params_changed)
        preprocessing_layout.addWidget(self.enable_preprocessing_cb)

        # 预处理方法选择
        methods_layout = QVBoxLayout()

        self.edge_detection_cb = QCheckBox("边缘检测")
        self.edge_detection_cb.stateChanged.connect(self.on_params_changed)
        methods_layout.addWidget(self.edge_detection_cb)

        self.histogram_equalization_cb = QCheckBox("直方图均衡化")
        self.histogram_equalization_cb.stateChanged.connect(self.on_params_changed)
        methods_layout.addWidget(self.histogram_equalization_cb)

        self.gamma_correction_cb = QCheckBox("Gamma校正")
        self.gamma_correction_cb.stateChanged.connect(self.on_params_changed)
        methods_layout.addWidget(self.gamma_correction_cb)

        self.gaussian_blur_cb = QCheckBox("高斯模糊")
        self.gaussian_blur_cb.stateChanged.connect(self.on_params_changed)
        methods_layout.addWidget(self.gaussian_blur_cb)

        self.sharpen_cb = QCheckBox("锐化")
        self.sharpen_cb.stateChanged.connect(self.on_params_changed)
        methods_layout.addWidget(self.sharpen_cb)

        preprocessing_layout.addLayout(methods_layout)
        layout.addWidget(preprocessing_group)

        # 边缘检测参数
        edge_params_group = QGroupBox("边缘检测参数")
        edge_params_layout = QVBoxLayout(edge_params_group)

        # 检测方法
        edge_method_layout = QHBoxLayout()
        edge_method_layout.addWidget(QLabel("检测方法:"))
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(["Canny", "Sobel", "Laplacian", "Scharr"])
        self.edge_method_combo.currentTextChanged.connect(self.on_params_changed)
        edge_method_layout.addWidget(self.edge_method_combo)
        edge_params_layout.addLayout(edge_method_layout)

        # 阈值设置
        low_threshold_layout = QHBoxLayout()
        low_threshold_layout.addWidget(QLabel("低阈值:"))
        self.edge_low_threshold_spinbox = QSpinBox()
        self.edge_low_threshold_spinbox.setMinimum(0)
        self.edge_low_threshold_spinbox.setMaximum(255)
        self.edge_low_threshold_spinbox.setValue(50)
        self.edge_low_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        low_threshold_layout.addWidget(self.edge_low_threshold_spinbox)
        edge_params_layout.addLayout(low_threshold_layout)

        high_threshold_layout = QHBoxLayout()
        high_threshold_layout.addWidget(QLabel("高阈值:"))
        self.edge_high_threshold_spinbox = QSpinBox()
        self.edge_high_threshold_spinbox.setMinimum(0)
        self.edge_high_threshold_spinbox.setMaximum(255)
        self.edge_high_threshold_spinbox.setValue(150)
        self.edge_high_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        high_threshold_layout.addWidget(self.edge_high_threshold_spinbox)
        edge_params_layout.addLayout(high_threshold_layout)

        layout.addWidget(edge_params_group)

        # Gamma校正参数
        gamma_group = QGroupBox("Gamma校正参数")
        gamma_layout = QVBoxLayout(gamma_group)

        gamma_value_layout = QHBoxLayout()
        gamma_value_layout.addWidget(QLabel("Gamma值:"))
        self.gamma_spinbox = QDoubleSpinBox()
        self.gamma_spinbox.setMinimum(0.1)
        self.gamma_spinbox.setMaximum(3.0)
        self.gamma_spinbox.setValue(1.0)
        self.gamma_spinbox.setSingleStep(0.1)
        self.gamma_spinbox.valueChanged.connect(self.on_params_changed)
        gamma_value_layout.addWidget(self.gamma_spinbox)
        gamma_layout.addLayout(gamma_value_layout)

        layout.addWidget(gamma_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "4. 图像处理")

    def create_text_content_tab(self):
        """创建文字内容控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 文字叠加控制
        text_overlay_group = QGroupBox("文字叠加控制")
        text_overlay_layout = QVBoxLayout(text_overlay_group)

        self.text_overlay_cb = QCheckBox("启用文字叠加")
        self.text_overlay_cb.stateChanged.connect(self.on_params_changed)
        text_overlay_layout.addWidget(self.text_overlay_cb)

        # 文字内容
        text_content_layout = QHBoxLayout()
        text_content_layout.addWidget(QLabel("文字内容:"))
        self.text_content_edit = QLineEdit()
        self.text_content_edit.setPlaceholderText("请输入要叠加的文字...")
        self.text_content_edit.textChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_content_edit)
        text_overlay_layout.addLayout(text_content_layout)

        layout.addWidget(text_overlay_group)

        # 文字样式控制
        text_style_group = QGroupBox("文字样式")
        text_style_layout = QVBoxLayout(text_style_group)

        # 字体设置
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体:"))
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Arial", "Times New Roman", "Courier", "Helvetica", "Comic Sans"
        ])
        self.font_family_combo.currentTextChanged.connect(self.on_params_changed)
        font_layout.addWidget(self.font_family_combo)
        text_style_layout.addLayout(font_layout)

        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setMinimum(8)
        self.font_size_spinbox.setMaximum(72)
        self.font_size_spinbox.setValue(24)
        self.font_size_spinbox.valueChanged.connect(self.on_params_changed)
        font_size_layout.addWidget(self.font_size_spinbox)
        text_style_layout.addLayout(font_size_layout)

        # 字体颜色
        font_color_layout = QHBoxLayout()
        font_color_layout.addWidget(QLabel("字体颜色:"))
        self.text_color_button = QPushButton("选择颜色")
        self.text_color = QColor(255, 255, 255)  # 默认白色
        self.text_color_button.clicked.connect(self.choose_text_color)
        self.update_color_button()
        font_color_layout.addWidget(self.text_color_button)
        text_style_layout.addLayout(font_color_layout)

        # 字体效果
        self.font_bold_cb = QCheckBox("粗体")
        self.font_bold_cb.stateChanged.connect(self.on_params_changed)
        text_style_layout.addWidget(self.font_bold_cb)

        self.enable_outline_cb = QCheckBox("启用描边")
        self.enable_outline_cb.stateChanged.connect(self.on_params_changed)
        text_style_layout.addWidget(self.enable_outline_cb)

        self.enable_shadow_cb = QCheckBox("启用阴影")
        self.enable_shadow_cb.stateChanged.connect(self.on_params_changed)
        text_style_layout.addWidget(self.enable_shadow_cb)

        layout.addWidget(text_style_group)

        # 文字位置控制
        text_position_group = QGroupBox("文字位置")
        text_position_layout = QVBoxLayout(text_position_group)

        # 位置模式
        text_position_mode_layout = QHBoxLayout()
        text_position_mode_layout.addWidget(QLabel("位置模式:"))
        self.text_position_mode_combo = QComboBox()
        self.text_position_mode_combo.addItems([
            "静态位置 (Static)",
            "动态运动 (Dynamic)"
        ])
        self.text_position_mode_combo.currentTextChanged.connect(self.on_text_position_mode_changed)
        text_position_mode_layout.addWidget(self.text_position_mode_combo)
        text_position_layout.addLayout(text_position_mode_layout)

        # 静态位置设置
        self.text_static_position_group = QGroupBox("静态位置")
        text_static_layout = QVBoxLayout(self.text_static_position_group)

        text_x_layout = QHBoxLayout()
        text_x_layout.addWidget(QLabel("X位置 (%):"))
        self.text_x_spinbox = QDoubleSpinBox()
        self.text_x_spinbox.setMinimum(0.0)
        self.text_x_spinbox.setMaximum(100.0)
        self.text_x_spinbox.setValue(10.0)
        self.text_x_spinbox.setSingleStep(1.0)
        self.text_x_spinbox.valueChanged.connect(self.on_params_changed)
        text_x_layout.addWidget(self.text_x_spinbox)
        text_static_layout.addLayout(text_x_layout)

        text_y_layout = QHBoxLayout()
        text_y_layout.addWidget(QLabel("Y位置 (%):"))
        self.text_y_spinbox = QDoubleSpinBox()
        self.text_y_spinbox.setMinimum(0.0)
        self.text_y_spinbox.setMaximum(100.0)
        self.text_y_spinbox.setValue(10.0)
        self.text_y_spinbox.setSingleStep(1.0)
        self.text_y_spinbox.valueChanged.connect(self.on_params_changed)
        text_y_layout.addWidget(self.text_y_spinbox)
        text_static_layout.addLayout(text_y_layout)

        text_position_layout.addWidget(self.text_static_position_group)

        # 动态运动设置
        self.text_dynamic_position_group = QGroupBox("动态运动")
        text_dynamic_layout = QVBoxLayout(self.text_dynamic_position_group)

        text_trajectory_layout = QHBoxLayout()
        text_trajectory_layout.addWidget(QLabel("运动轨迹:"))
        self.text_motion_trajectory_combo = QComboBox()
        self.text_motion_trajectory_combo.addItems([
            "水平滚动 (Horizontal)",
            "垂直滚动 (Vertical)",
            "圆形轨迹 (Circular)",
            "对角线 (Diagonal)"
        ])
        self.text_motion_trajectory_combo.currentTextChanged.connect(self.on_params_changed)
        text_trajectory_layout.addWidget(self.text_motion_trajectory_combo)
        text_dynamic_layout.addLayout(text_trajectory_layout)

        text_speed_layout = QHBoxLayout()
        text_speed_layout.addWidget(QLabel("运动速度:"))
        self.text_motion_speed_spinbox = QDoubleSpinBox()
        self.text_motion_speed_spinbox.setMinimum(0.1)
        self.text_motion_speed_spinbox.setMaximum(10.0)
        self.text_motion_speed_spinbox.setValue(1.0)
        self.text_motion_speed_spinbox.setSingleStep(0.1)
        self.text_motion_speed_spinbox.valueChanged.connect(self.on_params_changed)
        text_speed_layout.addWidget(self.text_motion_speed_spinbox)
        text_dynamic_layout.addLayout(text_speed_layout)

        text_position_layout.addWidget(self.text_dynamic_position_group)

        # 初始状态设置
        self.text_dynamic_position_group.setVisible(False)

        layout.addWidget(text_position_group)

        # 文字时间控制
        text_timing_group = QGroupBox("文字时间控制")
        text_timing_layout = QVBoxLayout(text_timing_group)

        # 出现频次
        appearance_layout = QHBoxLayout()
        appearance_layout.addWidget(QLabel("出现频次:"))
        self.text_appearance_frequency_spinbox = QSpinBox()
        self.text_appearance_frequency_spinbox.setMinimum(1)
        self.text_appearance_frequency_spinbox.setMaximum(100)
        self.text_appearance_frequency_spinbox.setValue(1)
        self.text_appearance_frequency_spinbox.valueChanged.connect(self.on_params_changed)
        appearance_layout.addWidget(self.text_appearance_frequency_spinbox)
        text_timing_layout.addLayout(appearance_layout)

        # 连续显示帧数
        continuous_layout = QHBoxLayout()
        continuous_layout.addWidget(QLabel("连续帧数:"))
        self.text_continuous_frames_spinbox = QSpinBox()
        self.text_continuous_frames_spinbox.setMinimum(1)
        self.text_continuous_frames_spinbox.setMaximum(1000)
        self.text_continuous_frames_spinbox.setValue(30)
        self.text_continuous_frames_spinbox.valueChanged.connect(self.on_params_changed)
        continuous_layout.addWidget(self.text_continuous_frames_spinbox)
        text_timing_layout.addLayout(continuous_layout)

        # 淡入淡出效果
        self.text_fade_effect_cb = QCheckBox("启用淡入淡出效果")
        self.text_fade_effect_cb.stateChanged.connect(self.on_params_changed)
        text_timing_layout.addWidget(self.text_fade_effect_cb)

        layout.addWidget(text_timing_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "5. 文字内容")

    def create_basic_fusion_tab(self):
        """创建基础融合设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 基础融合参数
        basic_params_group = QGroupBox("基础融合参数")
        basic_params_layout = QVBoxLayout(basic_params_group)

        # 透明度控制
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setMinimum(0)
        self.alpha_slider.setMaximum(100)
        self.alpha_slider.setValue(int(self.config.get("fusion.default_blend_alpha", 0.5) * 100))
        self.alpha_slider.valueChanged.connect(self.on_params_changed)
        alpha_layout.addWidget(self.alpha_slider)

        self.alpha_label = QLabel("50%")
        self.alpha_slider.valueChanged.connect(lambda v: self.alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.alpha_label)
        basic_params_layout.addLayout(alpha_layout)

        layout.addWidget(basic_params_group)

        # 叠加融合参数（仅叠加融合时显示）
        self.overlay_group = QGroupBox("叠加融合参数")
        overlay_layout = QVBoxLayout(self.overlay_group)

        # 叠加模式
        overlay_mode_layout = QHBoxLayout()
        overlay_mode_layout.addWidget(QLabel("叠加模式:"))
        self.overlay_mode_combo = QComboBox()
        self.overlay_mode_combo.addItems([
            "正常 (Normal)", "正片叠底 (Multiply)", "滤色 (Screen)",
            "叠加 (Overlay)", "柔光 (Soft Light)", "强光 (Hard Light)"
        ])
        self.overlay_mode_combo.currentTextChanged.connect(self.on_params_changed)
        overlay_mode_layout.addWidget(self.overlay_mode_combo)
        overlay_layout.addLayout(overlay_mode_layout)

        layout.addWidget(self.overlay_group)

        # 混合融合参数（仅混合融合时显示）
        self.blend_group = QGroupBox("混合融合参数")
        blend_layout = QVBoxLayout(self.blend_group)

        # 混合模式
        blend_mode_layout = QHBoxLayout()
        blend_mode_layout.addWidget(QLabel("混合模式:"))
        self.blend_mode_combo = QComboBox()
        self.blend_mode_combo.addItems([
            "线性 (Linear)", "加权 (Weighted)", "Alpha混合 (Alpha)",
            "羽化 (Feather)", "渐变 (Gradient)"
        ])
        self.blend_mode_combo.currentTextChanged.connect(self.on_params_changed)
        blend_mode_layout.addWidget(self.blend_mode_combo)
        blend_layout.addLayout(blend_mode_layout)

        # 羽化半径
        feather_layout = QHBoxLayout()
        feather_layout.addWidget(QLabel("羽化半径:"))
        self.feather_radius_spinbox = QSpinBox()
        self.feather_radius_spinbox.setMinimum(0)
        self.feather_radius_spinbox.setMaximum(50)
        self.feather_radius_spinbox.setValue(5)
        self.feather_radius_spinbox.valueChanged.connect(self.on_params_changed)
        feather_layout.addWidget(self.feather_radius_spinbox)
        blend_layout.addLayout(feather_layout)

        layout.addWidget(self.blend_group)

        # 初始状态设置
        self.overlay_group.setVisible(False)
        self.blend_group.setVisible(False)

        layout.addStretch()
        self.tab_widget.addTab(tab, "基础设置")
    
    def create_basic_params_group(self, parent_layout):
        """创建基础参数组"""
        group = QGroupBox("基础参数")
        layout = QVBoxLayout(group)

        # 透明度控制
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setMinimum(0)
        self.alpha_slider.setMaximum(100)
        self.alpha_slider.setValue(int(self.config.get("fusion.default_blend_alpha", 0.5) * 100))
        self.alpha_slider.valueChanged.connect(self.on_params_changed)
        alpha_layout.addWidget(self.alpha_slider)

        self.alpha_label = QLabel("50%")
        self.alpha_slider.valueChanged.connect(lambda v: self.alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.alpha_label)
        layout.addLayout(alpha_layout)

        # 调整模式
        resize_layout = QHBoxLayout()
        resize_layout.addWidget(QLabel("调整模式:"))
        self.resize_mode_combo = QComboBox()
        self.resize_mode_combo.addItems(["适应 (fit)", "拉伸 (stretch)", "裁剪 (crop)", "原始 (original)"])
        self.resize_mode_combo.currentTextChanged.connect(self.on_params_changed)
        resize_layout.addWidget(self.resize_mode_combo)
        layout.addLayout(resize_layout)

        # 帧范围控制
        frame_range_layout = QHBoxLayout()
        frame_range_layout.addWidget(QLabel("帧范围:"))
        self.start_frame_spinbox = QSpinBox()
        self.start_frame_spinbox.setMinimum(0)
        self.start_frame_spinbox.setMaximum(99999)
        self.start_frame_spinbox.setSuffix(" 开始")
        self.start_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.start_frame_spinbox)

        self.end_frame_spinbox = QSpinBox()
        self.end_frame_spinbox.setMinimum(1)
        self.end_frame_spinbox.setMaximum(99999)
        self.end_frame_spinbox.setValue(100)
        self.end_frame_spinbox.setSuffix(" 结束")
        self.end_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.end_frame_spinbox)
        layout.addLayout(frame_range_layout)

        parent_layout.addWidget(group)

    def create_advanced_fusion_tab(self):
        """创建高级融合标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 叠加融合参数
        self.overlay_group = QGroupBox("叠加融合参数")
        overlay_layout = QVBoxLayout(self.overlay_group)

        # 叠加模式
        overlay_mode_layout = QHBoxLayout()
        overlay_mode_layout.addWidget(QLabel("叠加模式:"))
        self.overlay_mode_combo = QComboBox()
        self.overlay_mode_combo.addItems([
            "正常 (Normal)", "正片叠底 (Multiply)", "滤色 (Screen)",
            "叠加 (Overlay)", "柔光 (Soft Light)", "强光 (Hard Light)"
        ])
        self.overlay_mode_combo.currentTextChanged.connect(self.on_params_changed)
        overlay_mode_layout.addWidget(self.overlay_mode_combo)
        overlay_layout.addLayout(overlay_mode_layout)

        # 叠加位置
        position_layout = QHBoxLayout()
        position_layout.addWidget(QLabel("位置:"))
        self.overlay_x_spinbox = QSpinBox()
        self.overlay_x_spinbox.setMinimum(0)
        self.overlay_x_spinbox.setMaximum(9999)
        self.overlay_x_spinbox.setSuffix(" X")
        self.overlay_x_spinbox.valueChanged.connect(self.on_params_changed)
        position_layout.addWidget(self.overlay_x_spinbox)

        self.overlay_y_spinbox = QSpinBox()
        self.overlay_y_spinbox.setMinimum(0)
        self.overlay_y_spinbox.setMaximum(9999)
        self.overlay_y_spinbox.setSuffix(" Y")
        self.overlay_y_spinbox.valueChanged.connect(self.on_params_changed)
        position_layout.addWidget(self.overlay_y_spinbox)
        overlay_layout.addLayout(position_layout)

        # 叠加尺寸
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("尺寸:"))
        self.overlay_width_spinbox = QSpinBox()
        self.overlay_width_spinbox.setMinimum(1)
        self.overlay_width_spinbox.setMaximum(9999)
        self.overlay_width_spinbox.setValue(200)
        self.overlay_width_spinbox.setSuffix(" 宽")
        self.overlay_width_spinbox.valueChanged.connect(self.on_params_changed)
        size_layout.addWidget(self.overlay_width_spinbox)

        self.overlay_height_spinbox = QSpinBox()
        self.overlay_height_spinbox.setMinimum(1)
        self.overlay_height_spinbox.setMaximum(9999)
        self.overlay_height_spinbox.setValue(150)
        self.overlay_height_spinbox.setSuffix(" 高")
        self.overlay_height_spinbox.valueChanged.connect(self.on_params_changed)
        size_layout.addWidget(self.overlay_height_spinbox)
        overlay_layout.addLayout(size_layout)

        # 移动轨迹
        self.overlay_moving_cb = QCheckBox("启用移动轨迹")
        self.overlay_moving_cb.stateChanged.connect(self.on_params_changed)
        overlay_layout.addWidget(self.overlay_moving_cb)

        layout.addWidget(self.overlay_group)

        # 混合融合参数
        self.blend_group = QGroupBox("混合融合参数")
        blend_layout = QVBoxLayout(self.blend_group)

        # 混合模式
        blend_mode_layout = QHBoxLayout()
        blend_mode_layout.addWidget(QLabel("混合模式:"))
        self.blend_mode_combo = QComboBox()
        self.blend_mode_combo.addItems([
            "线性 (Linear)", "加权 (Weighted)", "Alpha混合 (Alpha Blend)",
            "羽化 (Feather)", "渐变 (Gradient)"
        ])
        self.blend_mode_combo.currentTextChanged.connect(self.on_params_changed)
        blend_mode_layout.addWidget(self.blend_mode_combo)
        blend_layout.addLayout(blend_mode_layout)

        # 羽化半径
        feather_layout = QHBoxLayout()
        feather_layout.addWidget(QLabel("羽化半径:"))
        self.feather_radius_spinbox = QSpinBox()
        self.feather_radius_spinbox.setMinimum(0)
        self.feather_radius_spinbox.setMaximum(50)
        self.feather_radius_spinbox.setValue(5)
        self.feather_radius_spinbox.valueChanged.connect(self.on_params_changed)
        feather_layout.addWidget(self.feather_radius_spinbox)
        blend_layout.addLayout(feather_layout)

        layout.addWidget(self.blend_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "高级融合")
    
    def create_preprocessing_tab(self):
        """创建预处理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 边缘检测组
        edge_group = QGroupBox("边缘检测")
        edge_layout = QVBoxLayout(edge_group)

        self.edge_detection_cb = QCheckBox("启用边缘检测")
        self.edge_detection_cb.stateChanged.connect(self.on_params_changed)
        edge_layout.addWidget(self.edge_detection_cb)

        # 边缘检测方法
        edge_method_layout = QHBoxLayout()
        edge_method_layout.addWidget(QLabel("检测方法:"))
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(["Canny", "Sobel", "Laplacian", "Scharr"])
        self.edge_method_combo.currentTextChanged.connect(self.on_params_changed)
        edge_method_layout.addWidget(self.edge_method_combo)
        edge_layout.addLayout(edge_method_layout)

        # 边缘检测阈值
        edge_threshold_layout = QHBoxLayout()
        edge_threshold_layout.addWidget(QLabel("低阈值:"))
        self.edge_low_threshold_spinbox = QSpinBox()
        self.edge_low_threshold_spinbox.setMinimum(1)
        self.edge_low_threshold_spinbox.setMaximum(255)
        self.edge_low_threshold_spinbox.setValue(50)
        self.edge_low_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        edge_threshold_layout.addWidget(self.edge_low_threshold_spinbox)

        edge_threshold_layout.addWidget(QLabel("高阈值:"))
        self.edge_high_threshold_spinbox = QSpinBox()
        self.edge_high_threshold_spinbox.setMinimum(1)
        self.edge_high_threshold_spinbox.setMaximum(255)
        self.edge_high_threshold_spinbox.setValue(150)
        self.edge_high_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        edge_threshold_layout.addWidget(self.edge_high_threshold_spinbox)
        edge_layout.addLayout(edge_threshold_layout)

        layout.addWidget(edge_group)

        # 直方图处理组
        histogram_group = QGroupBox("直方图处理")
        histogram_layout = QVBoxLayout(histogram_group)

        self.histogram_match_cb = QCheckBox("启用直方图匹配")
        self.histogram_match_cb.stateChanged.connect(self.on_params_changed)
        histogram_layout.addWidget(self.histogram_match_cb)

        self.histogram_equalize_cb = QCheckBox("启用直方图均衡化")
        self.histogram_equalize_cb.stateChanged.connect(self.on_params_changed)
        histogram_layout.addWidget(self.histogram_equalize_cb)

        # Gamma校正
        gamma_layout = QHBoxLayout()
        gamma_layout.addWidget(QLabel("Gamma校正:"))
        self.gamma_spinbox = QDoubleSpinBox()
        self.gamma_spinbox.setMinimum(0.1)
        self.gamma_spinbox.setMaximum(3.0)
        self.gamma_spinbox.setValue(1.0)
        self.gamma_spinbox.setSingleStep(0.1)
        self.gamma_spinbox.valueChanged.connect(self.on_params_changed)
        gamma_layout.addWidget(self.gamma_spinbox)
        histogram_layout.addLayout(gamma_layout)

        layout.addWidget(histogram_group)

        # 图像滤镜组
        filter_group = QGroupBox("图像滤镜")
        filter_layout = QVBoxLayout(filter_group)

        # 滤镜类型
        filter_type_layout = QHBoxLayout()
        filter_type_layout.addWidget(QLabel("滤镜类型:"))
        self.filter_type_combo = QComboBox()
        self.filter_type_combo.addItems([
            "无", "高斯模糊", "运动模糊", "中值模糊",
            "双边滤波", "锐化", "浮雕", "降噪"
        ])
        self.filter_type_combo.currentTextChanged.connect(self.on_params_changed)
        filter_type_layout.addWidget(self.filter_type_combo)
        filter_layout.addLayout(filter_type_layout)

        # 滤镜强度
        filter_strength_layout = QHBoxLayout()
        filter_strength_layout.addWidget(QLabel("滤镜强度:"))
        self.filter_strength_slider = QSlider(Qt.Horizontal)
        self.filter_strength_slider.setMinimum(1)
        self.filter_strength_slider.setMaximum(20)
        self.filter_strength_slider.setValue(5)
        self.filter_strength_slider.valueChanged.connect(self.on_params_changed)
        filter_strength_layout.addWidget(self.filter_strength_slider)

        self.filter_strength_label = QLabel("5")
        self.filter_strength_slider.valueChanged.connect(
            lambda v: self.filter_strength_label.setText(str(v))
        )
        filter_strength_layout.addWidget(self.filter_strength_label)
        filter_layout.addLayout(filter_strength_layout)

        layout.addWidget(filter_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "预处理")
    
    def create_text_overlay_tab(self):
        """创建文字叠加标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 文字内容组
        text_content_group = QGroupBox("文字内容")
        text_content_layout = QVBoxLayout(text_content_group)

        self.text_overlay_cb = QCheckBox("启用文字叠加")
        self.text_overlay_cb.stateChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_overlay_cb)

        # 文字输入
        text_content_layout.addWidget(QLabel("文字内容:"))
        self.text_content_edit = QLineEdit()
        self.text_content_edit.setPlaceholderText("请输入要叠加的文字...")
        self.text_content_edit.textChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_content_edit)

        layout.addWidget(text_content_group)

        # 文字样式组
        text_style_group = QGroupBox("文字样式")
        text_style_layout = QVBoxLayout(text_style_group)

        # 样式预设
        style_preset_layout = QHBoxLayout()
        style_preset_layout.addWidget(QLabel("样式预设:"))
        self.text_style_combo = QComboBox()
        self.text_style_combo.addItems([
            "默认", "标题", "副标题", "水印", "警告", "自定义"
        ])
        self.text_style_combo.currentTextChanged.connect(self.on_text_style_changed)
        style_preset_layout.addWidget(self.text_style_combo)
        text_style_layout.addLayout(style_preset_layout)

        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setMinimum(8)
        self.font_size_spinbox.setMaximum(72)
        self.font_size_spinbox.setValue(24)
        self.font_size_spinbox.valueChanged.connect(self.on_params_changed)
        font_size_layout.addWidget(self.font_size_spinbox)
        text_style_layout.addLayout(font_size_layout)

        # 文字颜色
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("文字颜色:"))
        self.text_color_button = QPushButton()
        self.text_color_button.setFixedSize(50, 30)
        self.text_color_button.setStyleSheet("background-color: white; border: 1px solid black;")
        self.text_color_button.clicked.connect(self.choose_text_color)
        self.text_color = QColor(255, 255, 255)  # 默认白色
        color_layout.addWidget(self.text_color_button)
        text_style_layout.addLayout(color_layout)

        # 文字透明度
        text_alpha_layout = QHBoxLayout()
        text_alpha_layout.addWidget(QLabel("文字透明度:"))
        self.text_alpha_slider = QSlider(Qt.Horizontal)
        self.text_alpha_slider.setMinimum(0)
        self.text_alpha_slider.setMaximum(100)
        self.text_alpha_slider.setValue(100)
        self.text_alpha_slider.valueChanged.connect(self.on_params_changed)
        text_alpha_layout.addWidget(self.text_alpha_slider)

        self.text_alpha_label = QLabel("100%")
        self.text_alpha_slider.valueChanged.connect(
            lambda v: self.text_alpha_label.setText(f"{v}%")
        )
        text_alpha_layout.addWidget(self.text_alpha_label)
        text_style_layout.addLayout(text_alpha_layout)

        layout.addWidget(text_style_group)

        # 文字位置组
        text_position_group = QGroupBox("文字位置")
        text_position_layout = QVBoxLayout(text_position_group)

        # 位置预设
        position_preset_layout = QHBoxLayout()
        position_preset_layout.addWidget(QLabel("位置预设:"))
        self.text_position_combo = QComboBox()
        self.text_position_combo.addItems([
            "左上", "上中", "右上", "左中", "中心",
            "右中", "左下", "下中", "右下", "自定义"
        ])
        self.text_position_combo.currentTextChanged.connect(self.on_text_position_changed)
        position_preset_layout.addWidget(self.text_position_combo)
        text_position_layout.addLayout(position_preset_layout)

        # 自定义位置
        custom_pos_layout = QHBoxLayout()
        custom_pos_layout.addWidget(QLabel("自定义位置:"))
        self.text_x_spinbox = QSpinBox()
        self.text_x_spinbox.setMinimum(0)
        self.text_x_spinbox.setMaximum(9999)
        self.text_x_spinbox.setSuffix(" X")
        self.text_x_spinbox.valueChanged.connect(self.on_params_changed)
        custom_pos_layout.addWidget(self.text_x_spinbox)

        self.text_y_spinbox = QSpinBox()
        self.text_y_spinbox.setMinimum(0)
        self.text_y_spinbox.setMaximum(9999)
        self.text_y_spinbox.setSuffix(" Y")
        self.text_y_spinbox.valueChanged.connect(self.on_params_changed)
        custom_pos_layout.addWidget(self.text_y_spinbox)
        text_position_layout.addLayout(custom_pos_layout)

        # 文字动画
        self.text_animation_cb = QCheckBox("启用文字动画")
        self.text_animation_cb.stateChanged.connect(self.on_params_changed)
        text_position_layout.addWidget(self.text_animation_cb)

        layout.addWidget(text_position_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "文字叠加")

    def create_output_settings_tab(self):
        """创建输出设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 输出格式组
        output_format_group = QGroupBox("输出格式")
        output_format_layout = QVBoxLayout(output_format_group)

        # 视频编码
        codec_layout = QHBoxLayout()
        codec_layout.addWidget(QLabel("视频编码:"))
        self.output_codec_combo = QComboBox()
        self.output_codec_combo.addItems(["mp4v", "XVID", "H264", "MJPG"])
        self.output_codec_combo.currentTextChanged.connect(self.on_params_changed)
        codec_layout.addWidget(self.output_codec_combo)
        output_format_layout.addLayout(codec_layout)

        # 输出帧率
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("输出帧率:"))
        self.output_fps_spinbox = QDoubleSpinBox()
        self.output_fps_spinbox.setMinimum(1.0)
        self.output_fps_spinbox.setMaximum(120.0)
        self.output_fps_spinbox.setValue(30.0)
        self.output_fps_spinbox.setSingleStep(1.0)
        self.output_fps_spinbox.setSuffix(" fps")
        self.output_fps_spinbox.valueChanged.connect(self.on_params_changed)
        fps_layout.addWidget(self.output_fps_spinbox)
        output_format_layout.addLayout(fps_layout)

        # 输出质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("输出质量:"))
        self.output_quality_slider = QSlider(Qt.Horizontal)
        self.output_quality_slider.setMinimum(1)
        self.output_quality_slider.setMaximum(10)
        self.output_quality_slider.setValue(8)
        self.output_quality_slider.valueChanged.connect(self.on_params_changed)
        quality_layout.addWidget(self.output_quality_slider)

        self.output_quality_label = QLabel("8")
        self.output_quality_slider.valueChanged.connect(
            lambda v: self.output_quality_label.setText(str(v))
        )
        quality_layout.addWidget(self.output_quality_label)
        output_format_layout.addLayout(quality_layout)

        layout.addWidget(output_format_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "输出设置")
    
    def create_preview_controls(self, parent_layout):
        """创建预览控制"""
        preview_group = QGroupBox("实时预览")
        preview_layout = QVBoxLayout(preview_group)

        # 预览控制按钮
        preview_buttons_layout = QHBoxLayout()

        self.preview_button = QPushButton("生成预览")
        self.preview_button.clicked.connect(self.generate_preview)
        preview_buttons_layout.addWidget(self.preview_button)

        self.auto_preview_cb = QCheckBox("自动预览")
        self.auto_preview_cb.stateChanged.connect(self.on_auto_preview_changed)
        preview_buttons_layout.addWidget(self.auto_preview_cb)

        preview_layout.addLayout(preview_buttons_layout)

        # 预览帧数控制
        preview_frames_layout = QHBoxLayout()
        preview_frames_layout.addWidget(QLabel("预览帧数:"))
        self.preview_frames_spinbox = QSpinBox()
        self.preview_frames_spinbox.setMinimum(1)
        self.preview_frames_spinbox.setMaximum(10)
        self.preview_frames_spinbox.setValue(3)
        preview_frames_layout.addWidget(self.preview_frames_spinbox)
        preview_layout.addLayout(preview_frames_layout)

        parent_layout.addWidget(preview_group)

        # 设置自动预览定时器
        self.auto_preview_timer = QTimer()
        self.auto_preview_timer.setSingleShot(True)
        self.auto_preview_timer.timeout.connect(self.generate_preview)

    def on_fusion_type_changed(self, fusion_type: str):
        """融合类型改变 - 合并界面版本"""
        self.logger.info(f"融合类型改变: {fusion_type}")

        # 在合并界面中，所有控件都始终可见
        # 只需要记录融合类型改变即可
        # 具体的参数适用性由融合引擎处理

        self.on_params_changed()

    def on_position_mode_changed(self, mode: str):
        """空间位置模式改变"""
        if "静态位置" in mode:
            self.static_position_group.setVisible(True)
            self.dynamic_position_group.setVisible(False)
        else:
            self.static_position_group.setVisible(False)
            self.dynamic_position_group.setVisible(True)
        self.on_params_changed()

    def on_text_position_mode_changed(self, mode: str):
        """文字位置模式改变"""
        if "静态位置" in mode:
            self.text_static_position_group.setVisible(True)
            self.text_dynamic_position_group.setVisible(False)
        else:
            self.text_static_position_group.setVisible(False)
            self.text_dynamic_position_group.setVisible(True)
        self.on_params_changed()

    def on_text_style_changed(self, style: str):
        """文字样式改变"""
        # 根据预设样式更新参数
        if style == "标题":
            self.font_size_spinbox.setValue(36)
            self.text_color = QColor(255, 255, 0)  # 黄色
        elif style == "副标题":
            self.font_size_spinbox.setValue(24)
            self.text_color = QColor(255, 255, 255)  # 白色
        elif style == "水印":
            self.font_size_spinbox.setValue(16)
            self.text_color = QColor(200, 200, 200)  # 浅灰色
            self.text_alpha_slider.setValue(70)
        elif style == "警告":
            self.font_size_spinbox.setValue(28)
            self.text_color = QColor(255, 0, 0)  # 红色

        # 更新颜色按钮显示
        self.update_color_button()
        self.on_params_changed()

    def on_text_position_changed(self, position: str):
        """文字位置改变"""
        # 启用/禁用自定义位置控件
        is_custom = position == "自定义"
        self.text_x_spinbox.setEnabled(is_custom)
        self.text_y_spinbox.setEnabled(is_custom)

        self.on_params_changed()

    def choose_text_color(self):
        """选择文字颜色"""
        color = QColorDialog.getColor(self.text_color, self, "选择文字颜色")
        if color.isValid():
            self.text_color = color
            self.update_color_button()
            self.on_params_changed()

    def update_color_button(self):
        """更新颜色按钮显示"""
        color_name = self.text_color.name()
        self.text_color_button.setStyleSheet(
            f"background-color: {color_name}; border: 1px solid black;"
        )

    def on_auto_preview_changed(self, state):
        """自动预览状态改变"""
        if state == Qt.Checked:
            self.on_params_changed()  # 立即生成预览

    def generate_preview(self):
        """生成预览"""
        # 只发出预览请求信号，避免重复处理参数
        self.preview_requested.emit()
        self.logger.info("生成融合预览")

    def request_fusion(self):
        """请求开始融合"""
        self.fusion_requested.emit()
        self.logger.info("请求开始融合")

    def on_params_changed(self):
        """参数改变"""
        params = self.get_current_params()
        self.fusion_params_changed.emit(params)
        self.parameters_changed.emit(params)  # 兼容性信号
        self.logger.debug(f"融合参数改变")

        # 如果启用了自动预览，延迟生成预览
        if hasattr(self, 'auto_preview_cb') and self.auto_preview_cb.isChecked():
            self.auto_preview_timer.stop()
            self.auto_preview_timer.start(1000)  # 1秒延迟

    def get_current_params(self) -> dict:
        """获取当前参数 - 合并界面版本"""
        # 映射融合类型
        fusion_type_text = self.fusion_type_combo.currentText()
        if "插入融合" in fusion_type_text:
            fusion_type = "insertion"
        elif "叠加融合" in fusion_type_text:
            fusion_type = "overlay"
        elif "混合融合" in fusion_type_text:
            fusion_type = "blend"
        else:
            fusion_type = "insertion"  # 默认值

        # 获取预处理方法列表
        preprocessing_methods = []
        if hasattr(self, 'edge_detection_cb') and self.edge_detection_cb.isChecked():
            preprocessing_methods.append("edge_detection")
        if hasattr(self, 'histogram_cb') and self.histogram_cb.isChecked():
            preprocessing_methods.append("histogram_processing")
        if hasattr(self, 'filter_cb') and self.filter_cb.isChecked():
            preprocessing_methods.append("image_filter")

        params = {
            # 基础参数
            "fusion_type": fusion_type,
            "alpha": self.alpha_slider.value() / 100.0,

            # 1. 时间维度控制
            "time_dimension": {
                "insertion_mode": self.insertion_mode_combo.currentText().split()[0] if hasattr(self, 'insertion_mode_combo') else "direct",
                "insertion_count": self.insertion_count_spinbox.value() if hasattr(self, 'insertion_count_spinbox') else 5,
                "time_distribution": self.time_distribution_combo.currentText().split()[0] if hasattr(self, 'time_distribution_combo') else "random",
                "frame_range": (
                    self.start_frame_spinbox.value() if hasattr(self, 'start_frame_spinbox') else 0,
                    self.end_frame_spinbox.value() if hasattr(self, 'end_frame_spinbox') else 100
                )
            },

            # 2. 空间尺寸控制
            "spatial_size": {
                "resize_mode": self.scale_mode_combo.currentText().split()[0] if hasattr(self, 'scale_mode_combo') else "fit",
                "scale_ratio": self.scale_slider.value() / 100.0 if hasattr(self, 'scale_slider') else 1.0,
                "keep_aspect_ratio": True,  # 默认保持长宽比
                "alignment": "center",  # 默认居中对齐
                "align_to_video_a": self.size_align_cb.isChecked() if hasattr(self, 'size_align_cb') else True
            },

            # 3. 空间位置控制
            "spatial_position": {
                "position_mode": self.position_mode_combo.currentText().split()[0] if hasattr(self, 'position_mode_combo') else "static",
                "static_position": {
                    "x": self.x_position_slider.value() / 100.0 if hasattr(self, 'x_position_slider') else 0.5,
                    "y": self.y_position_slider.value() / 100.0 if hasattr(self, 'y_position_slider') else 0.5
                },
                "dynamic_motion": {
                    "trajectory": self.trajectory_combo.currentText().split()[0] if hasattr(self, 'trajectory_combo') else "horizontal",
                    "speed": self.movement_speed_slider.value() if hasattr(self, 'movement_speed_slider') else 5.0
                }
            },

            # 4. 图像内容处理控制
            "image_processing": {
                "enable_preprocessing": self.enable_preprocessing_cb.isChecked() if hasattr(self, 'enable_preprocessing_cb') else False,
                "preprocessing_methods": preprocessing_methods,
                "edge_detection": {
                    "method": self.edge_method_combo.currentText().lower() if hasattr(self, 'edge_method_combo') else "canny"
                },
                "histogram_processing": {
                    "method": self.histogram_method_combo.currentText() if hasattr(self, 'histogram_method_combo') else "equalization"
                },
                "image_filter": {
                    "type": self.filter_type_combo.currentText() if hasattr(self, 'filter_type_combo') else "gaussian_blur"
                },
                "blend_mode": self.blend_mode_combo.currentText().split()[0] if hasattr(self, 'blend_mode_combo') else "alpha"
            },

            # 5. 文字内容控制
            "text_content": {
                "enable_text_overlay": self.enable_text_cb.isChecked() if hasattr(self, 'enable_text_cb') else False,
                "text_content": self.text_content_edit.text() if hasattr(self, 'text_content_edit') else "",
                "font_size": self.font_size_slider.value() if hasattr(self, 'font_size_slider') else 24,
                "font_color": self.text_color_combo.currentText() if hasattr(self, 'text_color_combo') else "white",
                "text_position": self.text_position_combo.currentText().split()[0] if hasattr(self, 'text_position_combo') else "center",
                "text_animation": self.text_animation_combo.currentText().split()[0] if hasattr(self, 'text_animation_combo') else "none"
            },

            # 输出设置
            "output_settings": {
                "format": self.output_format_combo.currentText() if hasattr(self, 'output_format_combo') else "mp4",
                "quality": self.quality_slider.value() if hasattr(self, 'quality_slider') else 8,
                "fps": self.output_fps_spinbox.value() if hasattr(self, 'output_fps_spinbox') else 30
            }
        }

        return params

    def set_params(self, params: dict):
        """设置参数"""
        try:
            # 基础参数
            if "fusion_type" in params:
                index = self.fusion_type_combo.findText(params["fusion_type"])
                if index >= 0:
                    self.fusion_type_combo.setCurrentIndex(index)

            if "alpha" in params:
                self.alpha_slider.setValue(int(params["alpha"] * 100))

            # 更多参数设置...
            self.logger.info("参数设置完成")

        except Exception as e:
            self.logger.error(f"设置参数失败: {e}")

    def reset_params(self):
        """重置参数到默认值"""
        try:
            # 重置所有控件到默认值
            self.fusion_type_combo.setCurrentIndex(0)
            self.alpha_slider.setValue(50)
            self.resize_mode_combo.setCurrentIndex(0)
            self.start_frame_spinbox.setValue(0)
            self.end_frame_spinbox.setValue(100)

            # 重置其他控件...
            self.logger.info("参数重置完成")

        except Exception as e:
            self.logger.error(f"重置参数失败: {e}")

    def save_preset(self, name: str):
        """保存参数预设"""
        try:
            params = self.get_current_params()
            self.config.set(f"presets.{name}", params)
            self.config.save()
            self.logger.info(f"参数预设已保存: {name}")

        except Exception as e:
            self.logger.error(f"保存参数预设失败: {e}")

    def load_preset(self, name: str):
        """加载参数预设"""
        try:
            params = self.config.get(f"presets.{name}")
            if params:
                self.set_params(params)
                self.logger.info(f"参数预设已加载: {name}")
            else:
                self.logger.warning(f"参数预设不存在: {name}")

        except Exception as e:
            self.logger.error(f"加载参数预设失败: {e}")
